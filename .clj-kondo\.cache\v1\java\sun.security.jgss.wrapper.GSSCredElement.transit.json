["^ ", "~:members", ["^ ", "isInitiatorCredential", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "GSSCredElement", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "pCred", ["^2", [["^ ", "^3", ["^2", ["~:field", "~:final"]]]]], "getAcceptLifetime", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "doServicePermCheck", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "isAcceptorCredential", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInitLifetime", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "dispose", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "get<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMechanism", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "impersonate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]