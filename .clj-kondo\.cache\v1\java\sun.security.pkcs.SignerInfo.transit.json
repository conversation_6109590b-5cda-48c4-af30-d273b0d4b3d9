["^ ", "~:members", ["^ ", "makeSigAlg", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]]]], "digestAlgorithmId", ["^2", [["^ ", "^3", ["^2", ["~:field"]]]]], "SignerInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "unauthenticatedAttributes", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "encryptedDigest", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "getAuthenticatedAttributes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIssuerName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDigestAlgorithmId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "timestamp", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "getTimestamp", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDigestEncryptionAlgorithmId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCertificateSerialNumber", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "issuerName", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "verify", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "verifyAlgorithms", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getTsToken", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "authenticatedAttributes", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "digestEncryptionAlgorithmId", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "getEncryptedDigest", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "version", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "getUnauthenticatedAttributes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCertificate<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "certificateSerialNumber", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "getCertificate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "encode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]