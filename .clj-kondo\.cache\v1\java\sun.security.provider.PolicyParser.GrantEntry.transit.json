["^ ", "~:members", ["^ ", "<PERSON><PERSON><PERSON><PERSON>", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "codeBase", ["^2", [["^ ", "^3", ["^2", ["^5", "~:field"]]]]], "contains", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "permissionEntries", ["^2", [["^ ", "^3", ["^2", ["^5", "^7"]]]]], "signed<PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^5", "^7"]]]]], "remove", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clone", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "permissionElements", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "add", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "principals", ["^2", [["^ ", "^3", ["^2", ["^5", "^7"]]]]], "write", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]