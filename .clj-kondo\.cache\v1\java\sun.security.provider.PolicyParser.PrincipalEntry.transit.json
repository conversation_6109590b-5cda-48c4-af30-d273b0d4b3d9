["^ ", "~:members", ["^ ", "isWildcardName", ["~#set", [["^ ", "~:flags", ["^2", ["~:method"]]]]], "WILDCARD_NAME", ["^2", [["^ ", "^3", ["^2", ["~:public", "~:static", "~:field", "~:final"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getDisplayName", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getPrincipalName", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "principalClass", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "getPrincipalClass", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "PrincipalE<PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "isReplaceName", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "write", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getDisplayClass", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "REPLACE_NAME", ["^2", [["^ ", "^3", ["^2", ["^6", "^7", "^8", "^9"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "principalName", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "isWildcardClass", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "WILDCARD_CLASS", ["^2", [["^ ", "^3", ["^2", ["^6", "^7", "^8", "^9"]]]]]]]