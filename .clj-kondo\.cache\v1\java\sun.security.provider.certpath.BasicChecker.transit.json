["^ ", "~:members", ["^ ", "<PERSON><PERSON><PERSON><PERSON>", ["~#set", [["^ ", "~:flags", ["^2", ["~:method"]]]]], "init", ["^2", [["^ ", "^3", ["^2", ["^4", "~:public"]]]]], "isForwardCheckingSupported", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getSupportedExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "check", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "makeInheritedParamsKey", ["^2", [["^ ", "^3", ["^2", ["^4", "~:static"]]]]], "getPublicKey", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]]]]