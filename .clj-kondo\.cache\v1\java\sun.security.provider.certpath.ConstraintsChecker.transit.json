["^ ", "~:members", ["^ ", "Constraints<PERSON>hecker", ["~#set", [["^ ", "~:flags", ["^2", ["~:method"]]]]], "init", ["^2", [["^ ", "^3", ["^2", ["^4", "~:public"]]]]], "isForwardCheckingSupported", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getSupportedExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "check", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "mergeNameConstraints", ["^2", [["^ ", "^3", ["^2", ["^4", "~:static"]]]]], "mergeBasicConstraints", ["^2", [["^ ", "^3", ["^2", ["^4", "^;"]]]]]]]