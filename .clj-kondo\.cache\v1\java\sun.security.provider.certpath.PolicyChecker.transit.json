["^ ", "~:members", ["^ ", "ANY_POLICY", ["~#set", [["^ ", "~:flags", ["^2", ["~:static", "~:field", "~:final"]]]]], "processPolicies", ["^2", [["^ ", "^3", ["^2", ["~:method", "^4"]]]]], "mergePolicyMapping", ["^2", [["^ ", "^3", ["^2", ["^8", "^4"]]]]], "isForwardCheckingSupported", ["^2", [["^ ", "^3", ["^2", ["^8", "~:public"]]]]], "check", ["^2", [["^ ", "^3", ["^2", ["^8", "^;"]]]]], "mergeExplicitPolicy", ["^2", [["^ ", "^3", ["^2", ["^8", "^4"]]]]], "getSupportedExtensions", ["^2", [["^ ", "^3", ["^2", ["^8", "^;"]]]]], "getPolicyTree", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "init", ["^2", [["^ ", "^3", ["^2", ["^8", "^;"]]]]], "mergeInhibitAnyPolicy", ["^2", [["^ ", "^3", ["^2", ["^8", "^4"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]]]]