["^ ", "~:members", ["^ ", "SCARD_E_PROTO_MISMATCH", ["~#set", [["^ ", "~:flags", ["^2", ["~:static", "~:field", "~:final"]]]]], "SCARD_W_REMOVED_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_CANCELLED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_STATE_CHANGED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "TIMEOUT_INFINITE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_READER_UNSUPPORTED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCardEstablishContext", ["^2", [["^ ", "^3", ["^2", ["~:method", "^4", "~:native"]]]]], "SCARD_STATE_IGNORE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_F_COMM_ERROR", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_INVALID_HANDLE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_SHARE_DIRECT", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_F_WAITED_TOO_LONG", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_UNKNOWN_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_STATE_UNPOWERED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_NO_READERS_AVAILABLE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_STATE_EXCLUSIVE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCardBeginTransaction", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "SCARD_F_INTERNAL_ERROR", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_INVALID_ATR", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_SHARE_SHARED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_RESET_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_READER_UNAVAILABLE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCardTransmit", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "SCARD_E_NOT_TRANSACTED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_SHARING_VIOLATION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_W_UNRESPONSIVE_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_W_UNSUPPORTED_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_STATE_UNKNOWN", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^=", "~:public", "^4"]]]]], "SCARD_E_INSUFFICIENT_BUFFER", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_EJECT_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_NOT_READY", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_W_UNPOWERED_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_SERVICE_STOPPED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_INVALID_PARAMETER", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCardListReaders", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "SCARD_STATE_UNAWARE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_INVALID_VALUE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_NO_SERVICE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "WINDOWS_ERROR_INVALID_PARAMETER", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_SCOPE_GLOBAL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_UNSUPPORTED_FEATURE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_NO_MEMORY", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_SYSTEM_CANCELLED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_W_INSERTED_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCardConnect", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "SCARD_W_RESET_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_STATE_UNAVAILABLE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_SCOPE_TERMINAL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_STATE_EMPTY", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_CANT_DISPOSE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_SCOPE_SYSTEM", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_SHARE_EXCLUSIVE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_UNPOWER_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_PCI_TOO_SMALL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_SCOPE_USER", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "WINDOWS_ERROR_INVALID_HANDLE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_STATE_INUSE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_F_UNKNOWN_ERROR", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_STATE_MUTE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCardStatus", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "SCARD_STATE_PRESENT", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_TIMEOUT", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCardControl", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "checkAvailable", ["^2", [["^ ", "^3", ["^2", ["^=", "^4"]]]]], "SCardDisconnect", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "SCardGetStatusChange", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "SCardEndTransaction", ["^2", [["^ ", "^3", ["^2", ["^=", "^4", "^>"]]]]], "SCARD_STATE_ATRMATCH", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_NO_SMARTCARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_UNKNOWN_READER", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_CARD_UNSUPPORTED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_DUPLICATE_READER", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_LEAVE_CARD", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_S_SUCCESS", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_E_INVALID_TARGET", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]]]]