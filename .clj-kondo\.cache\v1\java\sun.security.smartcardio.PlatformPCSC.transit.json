["^ ", "~:members", ["^ ", "SCARD_SWALLOWED", ["~#set", [["^ ", "~:flags", ["^2", ["~:static", "~:field", "~:final"]]]]], "SCARD_POWERED", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "PlatformPCSC", ["^2", [["^ ", "^3", ["^2", ["~:method"]]]]], "SCARD_PROTOCOL_T1", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "run", ["^2", [["^ ", "^3", ["^2", ["^9", "~:public"]]]]], "SCARD_UNKNOWN", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_PROTOCOL_T0", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_NEGOTIABLE", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_ABSENT", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_PROTOCOL_RAW", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_PRESENT", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SCARD_SPECIFIC", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "initException", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]]]]