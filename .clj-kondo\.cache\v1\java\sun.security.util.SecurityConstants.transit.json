["^ ", "~:members", ["^ ", "SOCKET_LISTEN_ACTION", ["~#set", [["^ ", "~:flags", ["^2", ["~:public", "~:static", "~:field", "~:final"]]]]], "SET_PROXYSELECTOR_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SET_RESPONSECACHE_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SET_SOCKETIMPL_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "FILE_READLINK_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "PROPERTY_RW_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "REFLECTION_FACTORY_ACCESS_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "PROPERTY_READ_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SPECIFY_HANDLER_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "CREATE_CLASSLOADER_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SOCKET_RESOLVE_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "FILE_WRITE_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "MODIFY_THREAD_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SOCKET_CONNECT_ACCEPT_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "GET_COMBINER_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "PROVIDER_VER", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "GET_COOKIEHANDLER_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "PROPERTY_WRITE_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "LOCAL_LISTEN_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "GET_RESPONSECACHE_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "MODIFY_THREADGROUP_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "FILE_DELETE_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "ALL_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "CREATE_ACC_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "CHECK_MEMBER_ACCESS_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SOCKET_CONNECT_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SOCKET_ACCEPT_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SET_COOKIEHANDLER_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "GET_POLICY_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "FILE_EXECUTE_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "GET_CLASSLOADER_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "ACCESS_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "GET_STACK_TRACE_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "GET_PROXYSELECTOR_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "GET_PD_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "FILE_READ_ACTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "SUBCLASS_IMPLEMENTATION_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]]]]