["^ ", "~:members", ["^ ", "needSignatureFileBytes", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "SignatureFileVerifier", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "contains", ["^2", [["^ ", "^3", ["^2", ["^4", "~:static"]]]]], "process", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBlockExtension", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^8"]]]]], "isInMetaInf", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^8"]]]]], "needSignatureFile", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "updateSigners", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "matches", ["^2", [["^ ", "^3", ["^2", ["^4", "^8"]]]]], "isSigningRelated", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^8"]]]]], "isSubSet", ["^2", [["^ ", "^3", ["^2", ["^4", "^8"]]]]], "setSignatureFile", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isBlockOrSF", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^8"]]]]], "MAX_SIG_FILE_SIZE", ["^2", [["^ ", "^3", ["^2", ["^5", "^8", "~:field", "~:final"]]]]], "getWeakAlgorithms", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]]]]