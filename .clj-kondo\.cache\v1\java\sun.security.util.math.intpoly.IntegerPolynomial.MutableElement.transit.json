["^ ", "~:members", ["^ ", "setSum", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "conditionalSwapWith", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "conditionalSet", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setDifference", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "fixed", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSquare", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "MutableElement", ["^2", [["^ ", "^3", ["^2", ["^4", "~:protected"]]]]], "setValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setProduct", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAdditiveInverse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]