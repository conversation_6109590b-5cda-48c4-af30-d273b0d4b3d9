["^ ", "~:members", ["^ ", "getAllExtensions", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "hasUnsupportedCriticalExtension", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "NAME", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "delete", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setExtension", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUnparseableExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExtension", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "CertificateExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNameByOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "encode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]