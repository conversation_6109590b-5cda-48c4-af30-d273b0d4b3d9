["^ ", "~:members", ["^ ", "getExtensionId", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "extensionValue", ["^2", [["^ ", "^3", ["^2", ["~:field", "~:protected"]]]]], "extensionId", ["^2", [["^ ", "^3", ["^2", ["^7", "^8"]]]]], "getExtensionValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "critical", ["^2", [["^ ", "^3", ["^2", ["^7", "^8"]]]]], "getId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "Extension", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newExtension", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "encode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:final"]]], ["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isCritical", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]