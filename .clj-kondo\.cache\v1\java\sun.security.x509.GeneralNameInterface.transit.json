["^ ", "~:members", ["^ ", "NAME_ANY", ["~#set", [["^ ", "~:flags", ["^2", ["~:field"]]]]], "getType", ["^2", [["^ ", "^3", ["^2", ["~:method"]]]]], "constrains", ["^2", [["^ ", "^3", ["^2", ["^6"]]]]], "NAME_URI", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_IP", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_SAME_TYPE", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_WIDENS", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_DIFF_TYPE", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "subtreeDepth", ["^2", [["^ ", "^3", ["^2", ["^6"]]]]], "NAME_MATCH", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_NARROWS", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_EDI", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_RFC822", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_DNS", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_X400", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_OID", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "NAME_DIRECTORY", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]]]]