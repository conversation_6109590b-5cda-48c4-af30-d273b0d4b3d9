["^ ", "~:members", ["^ ", "NAME", ["~#set", [["^ ", "~:flags", ["^2", ["~:public", "~:static", "~:field", "~:final"]]]]], "PolicyConstraintsExtension", ["^2", [["^ ", "^3", ["^2", ["~:method", "^4"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "encode", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "getRequire", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "getInhibit", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]]]]