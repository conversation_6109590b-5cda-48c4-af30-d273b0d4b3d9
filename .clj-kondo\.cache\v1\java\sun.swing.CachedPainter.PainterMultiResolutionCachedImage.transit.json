["^ ", "~:members", ["^ ", "PainterMultiResolutionCachedImage", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "setParams", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getWidth", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHeight", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getResolutionVariant", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBaseImage", ["^2", [["^ ", "^3", ["^2", ["^4", "~:protected"]]]]], "getResolutionVariants", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]