["^ ", "~:members", ["^ ", "getInstance", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]]]], "getPreferredGap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getContainerGap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isLabelAndNonlabel", ["^2", [["^ ", "^3", ["^2", ["^4", "~:protected"]]]]], "getButtonGap", ["^2", [["^ ", "^3", ["^2", ["^4", "^:"]]], ["^ ", "^3", ["^2", ["^4", "^5"]]]]], "flipDirection", ["^2", [["^ ", "^3", ["^2", ["^4", "^:"]]]]], "getIndent", ["^2", [["^ ", "^3", ["^2", ["^4", "^:"]]]]]]]