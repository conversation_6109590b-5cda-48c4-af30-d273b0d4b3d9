["^ ", "~:members", ["^ ", "componentRemoved", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "overrideNativeWindowHandle", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setLayeredPane", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createDragGestureRecognizer", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getContentPane", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScaleFactorX", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "dispose", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getGraphics", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "run", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "JLightweightFrame", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "updateCursor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "notifyDisplayChanged", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "reshape", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createDragSourceContextPeer", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeDropTarget", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "propertyChange", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addNotify", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "ungrabFocus", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScaleFactor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isPaintingOrigin", ["^2", [["^ ", "^3", ["^2", ["^4", "~:protected"]]]]], "getGlassPane", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "paint", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setGlassPane", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "grabFocus", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "componentAdded", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRootPane", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setContentPane", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLayeredPane", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScaleFactorY", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addDropTarget", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]