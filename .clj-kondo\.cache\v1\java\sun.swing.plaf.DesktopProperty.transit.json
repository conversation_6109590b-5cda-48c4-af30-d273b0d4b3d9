["^ ", "~:members", ["^ ", "createValue", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getValueFromDesktop", ["^2", [["^ ", "^3", ["^2", ["^4", "~:protected"]]]]], "updateAllUIs", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "configureV<PERSON>ue", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "dispose", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "run", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDefaultValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "flushUnreferencedProperties", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "propertyChange", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "invalidate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "DesktopProperty", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "WeakPCL", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "updateUI", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]]]]