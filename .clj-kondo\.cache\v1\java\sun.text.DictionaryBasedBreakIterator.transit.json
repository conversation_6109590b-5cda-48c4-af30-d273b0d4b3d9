["^ ", "~:members", ["^ ", "handleNext", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:protected"]]]]], "preceding", ["^2", [["^ ", "^3", ["^2", ["^4", "~:public"]]]]], "setText", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "DictionaryBasedBreakIterator", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "previous", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "last", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "lookupCategory", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "following", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]], "first", ["^2", [["^ ", "^3", ["^2", ["^4", "^7"]]]]]]]