["^ ", "~:members", ["^ ", "JulianCalendar", ["~#set", [["^ ", "~:flags", ["^2", ["~:method"]]]]], "set<PERSON>ra", ["^2", [["^ ", "^3", ["^2", ["^4", "~:public"]]]]], "isLeapYear", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "getNormalizedYear", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getDayOfWeek", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "Date", ["^2", [["^ ", "^3", ["^2", ["^4", "~:protected"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "setKnownEra", ["^2", [["^ ", "^3", ["^2", ["^4", "^<"]]]]], "getYearFromFixedDate", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "newCalendarDate", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getCalendarDateFromFixedDate", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "setNormalizedYear", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getFixedDate", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "getCalendarDate", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]]]]