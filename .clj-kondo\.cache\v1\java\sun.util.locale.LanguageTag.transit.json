["^ ", "~:members", ["^ ", "canonicalizePrivateuse", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]]]], "PRIVATEUSE", ["^2", [["^ ", "^3", ["^2", ["^5", "^6", "~:field", "~:final"]]]]], "getScript", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "canonicalizeRegion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getPrivateuse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "PRIVUSE_VARIANT_PREFIX", ["^2", [["^ ", "^3", ["^2", ["^5", "^6", "^8", "^9"]]]]], "isVariant", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isExtlang", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getLanguage", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isPrivateuseSubtag", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "canonicalizeExtension", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isPrivateusePrefix", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getVariants", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isExtensionSingletonChar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isExtensionSingleton", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "canonicalizePrivateuseSubtag", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getExtlangs", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "canonicalizeExtensionSubtag", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isExtensionSubtag", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isPrivateusePrefixChar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "canonicalizeExtensionSingleton", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "SEP", ["^2", [["^ ", "^3", ["^2", ["^5", "^6", "^8", "^9"]]]]], "parse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "<PERSON><PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "canonicalizeScript", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "canonicalizeLanguage", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseLocale", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isScript", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "UNDETERMINED", ["^2", [["^ ", "^3", ["^2", ["^5", "^6", "^8", "^9"]]]]], "isRegion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "caseFoldTag", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getRegion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isLanguage", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "canonicalizeExtlang", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]]]]