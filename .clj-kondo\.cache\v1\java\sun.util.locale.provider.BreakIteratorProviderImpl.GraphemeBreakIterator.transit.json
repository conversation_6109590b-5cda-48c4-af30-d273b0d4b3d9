["^ ", "~:members", ["^ ", "next", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "GraphemeBreakIterator", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "offset", ["^2", [["^ ", "^3", ["^2", ["~:field"]]]]], "ci", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "setText", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "boundaries", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "getText", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "boundaryIndex", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "previous", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "current", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isBoundary", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "last", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "following", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "first", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]