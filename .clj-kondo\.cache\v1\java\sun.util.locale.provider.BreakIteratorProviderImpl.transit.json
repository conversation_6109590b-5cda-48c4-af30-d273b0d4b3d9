["^ ", "~:members", ["^ ", "next", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "GraphemeBreakIterator", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "offset", ["^2", [["^ ", "^3", ["^2", ["~:field"]]]]], "getCharacterInstance", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "ci", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "setText", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "BreakIteratorProviderImpl", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "boundaries", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "CharacterIteratorCharSequence", ["^2", [["^ ", "^3", ["^2", ["^4"]]]]], "getText", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "boundaryIndex", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "src", ["^2", [["^ ", "^3", ["^2", ["^8"]]]]], "subSequence", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLineInstance", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getWordInstance", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "char<PERSON>t", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "previous", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAvailableLanguageTags", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "current", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "length", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAvailableLocales", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isBoundary", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "last", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSentenceInstance", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSupportedLocale", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "following", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "first", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]