["^ ", "~:members", ["^ ", "of", ["~#set", [["^ ", "~:flags", ["^1", ["~:method", "~:static"]]]]], "setPlatformLevel", ["^1", [["^ ", "^2", ["^1", ["^3", "~:public"]]]]], "log", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "getName", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "logp", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "isLoggable", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "logrb", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "hashCode", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "toJUL", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "equals", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "getLoggerConfiguration", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "isEnabled", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]], "getPlatformLevel", ["^1", [["^ ", "^2", ["^1", ["^3", "^6"]]]]]]]