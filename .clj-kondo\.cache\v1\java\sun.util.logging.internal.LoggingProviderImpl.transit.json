["^ ", "~:members", ["^ ", "setLogManagerAccess", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]]]], "of", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "setPlatformLevel", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "log", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "logp", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isLoggable", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "LoggingProviderImpl", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "logrb", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLogManagerAccess", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "LOGGERFINDER_PERMISSION", ["^2", [["^ ", "^3", ["^2", ["^6", "~:field", "~:final"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "demandLoggerFor", ["^2", [["^ ", "^3", ["^2", ["^4", "~:protected"]]], ["^ ", "^3", ["^2", ["^4"]]]]], "toJUL", ["^2", [["^ ", "^3", ["^2", ["^4", "^6"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLoggerConfiguration", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isEnabled", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPlatformLevel", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]