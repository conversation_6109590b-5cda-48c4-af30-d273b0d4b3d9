select
    DISTINCT(t1.participation_time)  participation_time,
-- t1.participation_time,
t0.member_name,
t0.position_name,
t0.staff_id,
t0.contact_number,
t0.data_sources,
t0.department,
t0.member_status,
t1.user_id,
t1.member_id,
t1.phone_number,
concat(t1.member_name,'(',ifnull(t1.member_id,''),')') member_namenike,
t1.store_code,
t1.store_name,
t1.activity_name,
t1.participation_id,
concat(t1.product_name,'(',t1.product_code,')') product_name,
t1.cap_code,
t1.participation_time,
t2.distribution_time,
t1.award_name award_name_1,
t2.award_name award_name_2,
t2.amount,
t3.areacd,
t3.areanm,
t3.marketcd,
t3.marketnm,
t3.bigareacd,
t3.bigareanm,
t3.officecd,
t3.officenm,
t3.stationcd,
t3.stationnm,
t3.business_line,
t3.terminal_id,
t3.terminal_name,
t3.responsible_salesman_id,
t3.responsible_salesman_name,
t3.dealer_name_1,
t3.dealer_code_1
from
-- 人力
-- crb_edw_dm_sales.dmf_sales_personnel_info_df t0
crb_edw_dm_sales.dmf_sales_staff_month_info_df t0 
inner join 
iceberg.crb_edw_dws_sales.dws_sales_waiter_activity_participation_record_df t1 on t0.contact_number=t1.phone_number
inner join
iceberg.crb_edw_dws_sales.dws_sales_waiter_activity_award_record_df  t2
on t1.areaorg = t2.areaorg
and t1.marketorg = t2.marketorg
and t1.activity_id=t2.activity_id
and t1.participation_id= t2.participation_id
and t1.data_sources = t2.data_sources
left join (select 
headorg headcd,
headorgnm headnm,
regionorg areacd,
regionorgnm areanm,
marketorg marketcd,
marketorgnm marketnm,
bigareaorg bigareacd,
bigareaorgnm bigareanm,
officeorg officecd,
officeorgnm officenm,
stationorg stationcd,
stationorgnm stationnm,
terminal_id,
terminal_name,
business_line,
group_concat(distinct responsible_salesman_name) responsible_salesman_name,
group_concat(distinct responsible_salesman_id) responsible_salesman_id,
group_concat(distinct dealer_code_1) dealer_code_1,
group_concat(distinct dealer_name_1) dealer_name_1
from iceberg.crb_edw_dm_sales.dmf_sales_terminal_base_detail_df
group by headorg,
headorgnm,
marketorg,
marketorgnm,
regionorg,
regionorgnm,
bigareaorg,
bigareaorgnm,
officeorg,
officeorgnm,stationorg,stationorgnm,terminal_id,terminal_name,business_line) t3
on t1.crm_store_code=t3.terminal_id 
where 1=1
    ${if(len(总部)==0,"and 1=2","and t3.headcd = '"+总部+"'")}
    ${if(len(事业部)==0,"","and t3.areacd = '"+事业部+"'")}
    ${if(len(营销中心)==0,"","and t3.marketcd = '"+营销中心+"'")}
    ${if(len(销售大区)==0,"","and t3.bigareacd = '"+销售大区+"'")}
    ${if(len(业务部)==0,"","and t3.officecd= '"+业务部+"'")}
    ${if(len(异常电话)==0,"","and t0.contact_number like '%"+异常电话+"%'")}
    ${if(len(异常来源)==0,"","and t0.data_sources in ('"+SUBSTITUTE(异常来源,",","','")+"')")}
    ${if(len(活动名称)==0,"","and t1.activity_id in ('"+SUBSTITUTE(活动名称,",","','")+"')")}
    ${if(len(异常人员)==0,"","and (t1.member_name like '%"+异常人员+"%' or t1.user_id like '%"+异常人员+"%')")}
    ${if(len(开始时间)!=0 && len(结束时间)!=0," and participation_time BETWEEN '"+开始时间+" 00:00:00' and '"+结束时间+" 23:59:59'"," and participation_time BETWEEN '"+today()+" 00:00:00' and '"+today()+" 23:59:59'")}
    ${if(len(开始时间月)!=0 && len(结束时间月)!=0 && 开始时间 > DATE(2025,6,17) && 结束时间 > DATE(2025,6,17)," and t0.create_time BETWEEN '"+开始时间+" 00:00:00' and '"+结束时间+" 23:59:59'"," and t0.create_time BETWEEN '"+today()+" 00:00:00' and '"+today()+" 23:59:59'")}