-- =====================================================
-- 支付业务ODS到DWD层数据同步ETL
-- =====================================================
--
-- 功能说明：
-- 1. 从ODS层同步微信支付账单数据到DWD层
-- 2. 进行数据类型转换和数据质量校验
-- 3. 按日期分区进行增量数据处理
--
-- 修复内容：
-- 1. 修正数据类型转换，与目标表结构保持一致
-- 2. 优化时间字段处理逻辑
-- 3. 增强数据质量校验条件
-- 4. 添加详细的中文注释
--
-- 作者：华润数据开发团队
-- 修复时间：2025-06-30
-- =====================================================

INSERT OVERWRITE TABLE crb_edw_dwd_sales.pay_wx_bill
SELECT
    -- 主键字段：转换为INT类型（与目标表一致）
    CAST(id AS INT) AS id,

    -- 商户信息
    mch_id,                                    -- 商户号ID
    CAST(group_id AS INT) AS group_id,         -- 集团ID

    -- 时间字段：保持TIMESTAMP类型
    created_time,                              -- 创建时间
    modified_time,                             -- 最后更新时间
    CAST(record_date AS TIMESTAMP) AS record_date,  -- 记录日期
    check_date,                                -- 对账日期

    -- 交易标识字段
    trade_no,                                  -- 订单号
    batch_no,                                  -- 批次号
    bill_no,                                   -- 账单号

    -- 业务字段
    business_name,                             -- 业务名称
    business_type,                             -- 业务类型
    fund_type,                                 -- 收支类型

    -- 金额字段：保持INT类型（与目标表一致，单位：分）
    CAST(fund_amount AS INT) AS fund_amount,   -- 资金金额
    CAST(balance AS INT) AS balance,           -- 余额

    -- 操作人员信息
    fund_update_user,                          -- 资金操作人
    creator,                                   -- 创建人
    modifier,                                  -- 最后更新人员

    -- 备注信息
    remark,                                    -- 备注

    -- ETL时间：转换为STRING类型（与目标表一致）
    DATE_FORMAT(CURRENT_TIMESTAMP(), 'yyyy-MM-dd HH:mm:ss') AS etl_time

FROM crb_ods_aichuang.pay_wx_bill
WHERE 1=1
    -- 时间范围过滤：使用DATE函数确保日期比较准确性
    AND DATE(record_date) = DATE('${target_date}')

    -- 数据质量校验条件
    AND fund_amount IS NOT NULL               -- 资金金额不能为空
    AND fund_amount != 0                      -- 排除金额为0的记录
    AND trade_no IS NOT NULL                  -- 订单号不能为空
    AND trade_no != ''                        -- 排除空字符串订单号
    AND mch_id IS NOT NULL                    -- 商户号不能为空
    AND mch_id != ''                          -- 排除空字符串商户号
    AND business_type IS NOT NULL             -- 业务类型不能为空
    AND fund_type IS NOT NULL                 -- 收支类型不能为空

    -- 排除异常数据
    AND LENGTH(trade_no) >= 10                -- 订单号长度校验
    AND LENGTH(mch_id) >= 8                   -- 商户号长度校验

    -- 时间逻辑校验
    AND created_time <= modified_time         -- 创建时间应早于或等于修改时间
    AND record_date >= DATE('2020-01-01')     -- 排除异常历史数据
    AND record_date <= CURRENT_DATE()         -- 排除未来日期数据;

-- =====================================================
-- ETL执行后数据质量验证查询（可选执行）
-- =====================================================

-- 验证当日数据同步情况
-- SELECT
--     '数据同步验证' AS 检查项目,
--     COUNT(*) AS 同步记录数,
--     SUM(CASE WHEN fund_type = '收入' THEN fund_amount ELSE 0 END) AS 收入总金额,
--     SUM(CASE WHEN fund_type = '支出' THEN fund_amount ELSE 0 END) AS 支出总金额,
--     COUNT(DISTINCT mch_id) AS 涉及商户数,
--     COUNT(DISTINCT business_type) AS 业务类型数,
--     MIN(record_date) AS 最早记录时间,
--     MAX(record_date) AS 最晚记录时间
-- FROM crb_edw_dwd_sales.pay_wx_bill
-- WHERE DATE(record_date) = DATE('${target_date}');

-- 数据完整性检查
-- SELECT
--     '数据完整性检查' AS 检查项目,
--     COUNT(CASE WHEN id IS NULL THEN 1 END) AS 空主键数,
--     COUNT(CASE WHEN trade_no IS NULL OR trade_no = '' THEN 1 END) AS 空订单号数,
--     COUNT(CASE WHEN mch_id IS NULL OR mch_id = '' THEN 1 END) AS 空商户号数,
--     COUNT(CASE WHEN fund_amount = 0 THEN 1 END) AS 零金额记录数,
--     COUNT(CASE WHEN created_time > modified_time THEN 1 END) AS 时间逻辑异常数
-- FROM crb_edw_dwd_sales.pay_wx_bill
-- WHERE DATE(record_date) = DATE('${target_date}');

-- =====================================================
-- 性能优化建议
-- =====================================================
--
-- 1. 源表索引优化：
--    确保ODS表crb_ods_aichuang.pay_wx_bill已创建以下索引：
--    - idx_record_date (record_date) - 用于时间范围过滤
--    - idx_group_id_fund_type_check_date (group_id, fund_type, check_date) - 复合查询优化
--
-- 2. 目标表分区策略：
--    建议按record_date字段进行日分区，提高查询性能：
--    PARTITIONED BY (record_date_partition DATE)
--
-- 3. ETL执行优化：
--    - 建议在业务低峰期执行ETL任务
--    - 可考虑使用INSERT INTO代替INSERT OVERWRITE以支持增量更新
--    - 设置合适的并行度参数提高执行效率
--
-- 4. 监控告警：
--    - 监控每日同步数据量，设置异常告警阈值
--    - 监控数据质量指标，及时发现数据异常
--    - 设置ETL执行时间监控，确保及时完成
--
-- =====================================================


CREATE TABLE spark_catalog.crb_edw_dwd_sales.pay_wx_bill (
  balance INT NOT NULL COMMENT '余额',
  batch_no STRING NOT NULL COMMENT '批次号',
  bill_no STRING NOT NULL COMMENT '账单号',
  business_name STRING NOT NULL COMMENT '业务名称',
  business_type STRING NOT NULL COMMENT '业务类型',
  check_date DATE NOT NULL COMMENT '对账日期',
  created_time TIMESTAMP NOT NULL COMMENT '创建时间',
  creator STRING NOT NULL COMMENT '创建人',
  fund_amount INT NOT NULL COMMENT '资金金额',
  fund_type STRING NOT NULL COMMENT '收支类型',
  fund_update_user STRING NOT NULL COMMENT '资金操作人',
  group_id INT NOT NULL COMMENT '集团ID',
  id INT NOT NULL COMMENT '主键',
  mch_id STRING NOT NULL COMMENT '商户号ID',
  modified_time TIMESTAMP NOT NULL COMMENT '最后更新时间',
  modifier STRING NOT NULL COMMENT '最后更新人员',
  record_date TIMESTAMP NOT NULL COMMENT '记录日期',
  remark STRING COMMENT '备注',
  trade_no STRING NOT NULL COMMENT '订单号',
  etl_time STRING COMMENT 'ETL时间')
USING iceberg
COMMENT '商户号交易记录'


	CREATE TABLE crb_ods_aichuang.`pay_wx_bill` (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_id` int NOT NULL,
  `mch_id` varchar(32) NOT NULL,
  `record_date` datetime NOT NULL,
  `check_date` date NOT NULL,
  `batch_no` varchar(100) NOT NULL,
  `trade_no` varchar(100) NOT NULL,
  `business_name` varchar(50) NOT NULL,
  `business_type` varchar(50) NOT NULL,
  `fund_type` varchar(50) NOT NULL,
  `fund_amount` int NOT NULL,
  `balance` int NOT NULL,
  `bill_no` varchar(100) NOT NULL,
  `fund_update_user` varchar(50) NOT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `creator` varchar(50) NOT NULL,
  `created_time` datetime NOT NULL,
  `modifier` varchar(50) NOT NULL,
  `modified_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_group_id_bill_no` (`group_id`,`bill_no`),
  KEY `idx_group_id_fund_type_check_date` (`group_id`,`fund_type`,`check_date`),
  KEY `idx_record_date` (`record_date`)
) ENGINE=InnoDB AUTO_INCREMENT=36599687 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci