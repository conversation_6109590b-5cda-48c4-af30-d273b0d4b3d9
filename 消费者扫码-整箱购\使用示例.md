# 整箱购扫码数据分析SQL使用指南

## 概述
这个SQL查询用于分析整箱购扫码活动的数据，支持初期、期末、同比去年三个时间段的对比分析。

## 主要功能
- **多时间段对比**：初期 vs 期末 vs 去年同期
- **全面指标分析**：扫码量、扫码人数、扫码金额、人均指标等
- **红包vs实物奖励**：区分微信红包和实物奖励的金额统计
- **同比增长率**：自动计算各项指标的同比增长百分比

## 输出字段说明

### 基础信息
- `营销中心`：营销中心名称
- `活动ID`：活动编码
- `活动名称`：活动名称
- `SKU`：产品简称
- `产品名称`：ERP产品名称
- `产品编码`：ERP产品编码
- `上市时间`：产品上市时间（需要补充数据源）

### 初期时间段指标
- `初期总销量_箱`：初期总销量（箱数，需要补充销量数据）
- `初期总扫码量`：初期总扫码次数
- `初期扫码人数_去重`：初期扫码用户数（去重）
- `初期扫码金额_红包`：初期微信红包金额
- `初期非红包金额_实物奖等`：初期实物奖励金额
- `初期人均扫码量`：初期人均扫码次数
- `初期人均扫码金额_元`：初期人均扫码金额
- `初期次均扫码金额_元`：初期次均扫码金额
- `初期扫码比`：初期扫码量/销量比例

### 期末时间段指标
- `期末总销量_箱`：期末总销量（箱数）
- `期末总扫码量`：期末总扫码次数
- `期末扫码人数_去重`：期末扫码用户数（去重）
- `期末扫码金额_红包`：期末微信红包金额
- `期末非红包金额_实物奖等`：期末实物奖励金额
- `期末扫码比`：期末扫码量/销量比例

### 同比指标
- `总销量_箱_同比`：总销量同比增长率
- `总扫码量同比`：总扫码量同比增长率
- `扫码人数_去重_同比`：扫码人数同比增长率
- `扫码金额_红包_同比`：红包金额同比增长率
- `非红包金额_实物奖等_同比`：实物奖励金额同比增长率
- `扫码比同比`：扫码比同比增长率

## 使用步骤

### 1. 替换时间参数
将SQL中的时间参数替换为实际日期：

```sql
-- 将以下参数替换为实际日期
'${初期开始时间}' → '2024-01-01'
'${初期结束时间}' → '2024-01-31'
'${期末开始时间}' → '2024-02-01'
'${期末结束时间}' → '2024-02-29'
```

### 2. 添加过滤条件（可选）
在基础CTE中添加过滤条件以提升性能：

```sql
-- 在base_activity_product CTE中添加
WHERE fbpad.marketorg IN ('指定营销中心编码')
AND fbpad.code IN ('指定活动编码')
AND apmd.erp_product_code IN ('指定产品编码')
```

### 3. 补充销量数据（可选）
当前销量字段设置为0，可以根据业务需要关联销量数据表：

```sql
-- 示例：关联销量数据表
LEFT JOIN sales_data_table sdt 
    ON bap.产品编码 = sdt.product_code
    AND sdt.sales_date BETWEEN '初期开始时间' AND '初期结束时间'
```

## 性能优化建议

### 索引建议
```sql
-- 建议创建以下索引
CREATE INDEX idx_award_record_time ON dws_sales_consumer_activity_award_record_df(distribution_time);
CREATE INDEX idx_award_record_activity ON dws_sales_consumer_activity_award_record_df(activity_id, marketorg);
CREATE INDEX idx_award_record_user ON dws_sales_consumer_activity_award_record_df(participation_record_id, user_id);
```

### 查询优化
- 添加具体的营销中心或活动ID过滤条件
- 使用合适的时间范围，避免查询过大的数据集
- 考虑使用分区表提升查询性能

## 注意事项

1. **时间参数**：必须替换所有时间参数才能正常执行
2. **数据完整性**：使用FULL OUTER JOIN确保所有时间段数据完整
3. **除零处理**：同比计算中已处理除零异常
4. **NULL值处理**：所有数值字段都使用COALESCE处理NULL值
5. **销量数据**：当前销量相关字段为0，需要根据实际业务补充

## 示例查询

```sql
-- 查询2024年1-2月的整箱购扫码数据分析
-- 将时间参数替换后执行完整SQL即可
```

## 常见问题

**Q: 为什么销量字段显示为0？**
A: 当前SQL中销量字段设置为占位符0，需要根据实际业务关联销量数据表。

**Q: 如何提升查询性能？**
A: 建议添加营销中心、活动ID等过滤条件，并确保相关字段有索引。

**Q: 同比计算的逻辑是什么？**
A: 使用标准同比公式：(当前值 - 去年值) / 去年值 × 100%，并处理了除零异常。
