/*
================================================================================
整箱购扫码数据分析SQL - 多时间段对比分析
================================================================================

功能说明：
获取整箱扫码的金额数据，按初期、期末、同比去年三个时间段进行对比分析
支持营销中心、活动ID、SKU、产品等多维度的扫码行为分析

输出字段：
- 基础信息：营销中心、活动ID、SKU、产品名称、产品编码、上市时间
- 初期指标：总销量(箱)、总扫码量、扫码人数(去重)、扫码金额(红包)、非红包金额(实物奖等)、人均扫码量、人均扫码金额、次均扫码金额、扫码比
- 期末指标：总销量(箱)、总扫码量、扫码人数(去重)、扫码金额(红包)、非红包金额(实物奖等)、扫码比
- 同比指标：总销量同比、总扫码量同比、扫码人数同比、扫码金额同比、非红包金额同比、扫码比同比

时间段说明：
- 初期：用户指定的初始时间段（需要替换${初期开始时间}和${初期结束时间}参数）
- 期末：用户指定的结束时间段（需要替换${期末开始时间}和${期末结束时间}参数）
- 同比去年：去年同期数据用于计算同比增长率（自动计算去年同期时间）

指标计算说明：
- 扫码金额（红包）：send_type = 'WX' 的中奖金额
- 非红包金额（实物奖等）：send_type != 'WX' 的中奖金额
- 人均扫码量：总扫码量 ÷ 扫码人数（去重）
- 人均扫码金额：总扫码金额 ÷ 扫码人数（去重）
- 次均扫码金额：总扫码金额 ÷ 总扫码量
- 扫码比：扫码量 ÷ 销量（需要补充销量数据源）
- 同比增长率：(当前值 - 去年值) ÷ 去年值 × 100%

使用说明：
1. 替换时间参数：将${初期开始时间}、${初期结束时间}、${期末开始时间}、${期末结束时间}替换为实际日期
2. 销量数据：当前销量字段设置为0，需要根据实际业务补充销量数据表关联
3. 产品上市时间：需要补充产品维度表获取上市时间信息

性能优化：
- 在 distribution_time 字段上创建分区索引
- 在 activity_id, marketorg 字段上创建复合索引
- 在 participation_record_id, user_id 字段上创建索引
- 查询时添加具体的营销中心或活动ID过滤条件以提升性能

注意事项：
- 使用FULL OUTER JOIN确保所有时间段数据完整性
- 同比计算中处理了除零异常，避免SQL执行错误
- 所有金额和数量字段都使用COALESCE处理NULL值
*/

-- CTE 1: 基础活动和产品信息
-- 获取整箱购活动的基础信息，包括营销中心、活动编码、产品信息等
-- 性能优化：添加具体的营销中心或活动过滤条件
WITH base_activity_product AS (
    SELECT
        fbpad.marketorg,                           -- 营销中心编码
        fbpad.marketorgnm AS 营销中心,              -- 营销中心名称
        fbpad.code AS 活动编码,                     -- 活动编码（活动ID）
        fbpad.name AS 活动名称,                     -- 活动名称
        apmd.erp_product_code AS 产品编码,          -- ERP产品编码
        apmd.erp_product_name AS 产品名称,          -- ERP产品名称
        apmd.product_abbreviation AS SKU,          -- 产品简称（SKU）
        -- 产品上市时间（需要从产品维度表获取，暂时设置为NULL）
        CAST(NULL AS DATE) AS 上市时间
    FROM iceberg.crb_edw_dim_sales.dim_sales_full_box_purchase_activity_df fbpad
    LEFT JOIN iceberg.crb_edw_dim_sales.dim_sales_activity_product_mapping_df apmd
        ON fbpad.marketorg = apmd.marketorg
        AND apmd.statistical_dimension_code = fbpad.code
    -- 性能优化：可根据需要添加以下过滤条件
    WHERE 1=1   
      ${if(len(营销中心)==0,"","and fbpad.marketorg = '"+营销中心+"'")}
      ${if(len(产品编码)==0,"","and apmd.erp_product_code = '"+产品编码+"'")}
    -- AND fbpad.code IN ('指定活动编码')
),

-- CTE 2: 初期扫码数据统计
-- 统计初期时间段内的扫码相关指标
-- 数据来源：dws_sales_consumer_activity_award_record_df（消费者活动中奖记录表）
-- 关键逻辑：按活动和产品维度聚合扫码数据，区分红包和实物奖励
initial_period_data AS (
    SELECT
        bap.营销中心,
        bap.活动编码,
        bap.活动名称,
        bap.产品编码,
        bap.产品名称,
        bap.SKU,
        bap.上市时间,
        -- 初期总销量(箱) - 暂时设置为0，需要从销量数据表获取
        0 AS 初期总销量_箱,
        -- 初期总扫码量（根据参与记录ID去重）
        COUNT(DISTINCT caard.participation_record_id) AS 初期总扫码量,
        -- 初期扫码人数（根据用户ID去重）
        COUNT(DISTINCT caard.user_id) AS 初期扫码人数,
        -- 初期扫码金额（红包）- 微信红包类型
        SUM(CASE WHEN caard.send_type = 'WX' THEN caard.winning_amount ELSE 0 END) AS 初期扫码金额_红包,
        -- 初期非红包金额（实物奖等）- 非微信红包类型
        SUM(CASE WHEN caard.send_type != 'WX' THEN caard.winning_amount ELSE 0 END) AS 初期非红包金额_实物奖等,
        -- 初期人均扫码量 = 总扫码量 / 扫码人数
        CASE
            WHEN COUNT(DISTINCT caard.user_id) = 0 THEN 0
            ELSE COUNT(DISTINCT caard.participation_record_id) * 1.0 / COUNT(DISTINCT caard.user_id)
        END AS 初期人均扫码量,
        -- 初期人均扫码金额 = 总扫码金额 / 扫码人数
        CASE
            WHEN COUNT(DISTINCT caard.user_id) = 0 THEN 0
            ELSE  ROUND(SUM(caard.winning_amount) / COUNT(DISTINCT caard.user_id),2)
        END AS 初期人均扫码金额,
        -- 初期次均扫码金额 = 总扫码金额 / 扫码量
        CASE
            WHEN COUNT(DISTINCT caard.participation_record_id) = 0 THEN 0
            ELSE ROUND(SUM(caard.winning_amount) / COUNT(DISTINCT caard.participation_record_id),2)
        END AS 初期次均扫码金额,
        -- 初期扫码比 = 扫码量 / 销量（暂时设置为0，需要销量数据）
        0 AS 初期扫码比
    FROM base_activity_product bap
    LEFT JOIN iceberg.crb_edw_dws_sales.dws_sales_consumer_activity_award_record_df caard
        ON caard.activity_id = bap.活动编码
        AND caard.marketorg = bap.marketorg
        -- 初期时间段过滤条件（用户可根据需要修改日期范围）
        -- AND caard.distribution_time >= '${初期开始时间}'
        -- AND caard.distribution_time <= '${初期结束时间}'
 ${if(len(初期开始时间)!=0 && len(初期结束时间)!=0," and caard.distribution_time BETWEEN '"+初期开始时间+" 00:00:00:000000' and '"+初期结束时间+" 23:59:59:999999'","")}

    GROUP BY
        bap.营销中心,
        bap.活动编码,
        bap.活动名称,
        bap.产品编码,
        bap.产品名称,
        bap.SKU,
        bap.上市时间
),

-- CTE 3: 期末扫码数据统计
-- 统计期末时间段内的扫码相关指标
-- 数据来源：同初期数据，但时间范围为期末时间段
-- 用途：与初期数据对比，分析扫码趋势变化
final_period_data AS (
    SELECT
        bap.营销中心,
        bap.活动编码,
        bap.活动名称,
        bap.产品编码,
        bap.产品名称,
        bap.SKU,
        -- 期末总销量(箱) - 暂时设置为0，需要从销量数据表获取
        0 AS 期末总销量_箱,
        -- 期末总扫码量（根据参与记录ID去重）
        COUNT(DISTINCT caard.participation_record_id) AS 期末总扫码量,
        -- 期末扫码人数（根据用户ID去重）
        COUNT(DISTINCT caard.user_id) AS 期末扫码人数,
        -- 期末扫码金额（红包）- 微信红包类型
        SUM(CASE WHEN caard.send_type = 'WX' THEN caard.winning_amount ELSE 0 END) AS 期末扫码金额_红包,
        -- 期末非红包金额（实物奖等）- 非微信红包类型
        SUM(CASE WHEN caard.send_type != 'WX' THEN caard.winning_amount ELSE 0 END) AS 期末非红包金额_实物奖等,
        -- 期末扫码比 = 扫码量 / 销量（暂时设置为0，需要销量数据）
        0 AS 期末扫码比
    FROM base_activity_product bap
    LEFT JOIN iceberg.crb_edw_dws_sales.dws_sales_consumer_activity_award_record_df caard
        ON caard.activity_id = bap.活动编码
        AND caard.marketorg = bap.marketorg
        -- 期末时间段过滤条件（用户可根据需要修改日期范围）
        -- AND caard.distribution_time >= '${期末开始时间}'
        -- AND caard.distribution_time <= '${期末结束时间}'
         ${if(len(期末开始时间)!=0 && len(期末结束时间)!=0," and caard.distribution_time BETWEEN '"+期末开始时间+" 00:00:00:000000' and '"+期末结束时间+" 23:59:59:999999'","")}
    GROUP BY
        bap.营销中心,
        bap.活动编码,
        bap.活动名称,
        bap.产品编码,
        bap.产品名称,
        bap.SKU
),

-- CTE 4: 同比去年数据统计
-- 统计去年同期的扫码相关指标，用于计算同比增长率
-- 时间计算：将当前时间段向前推移一年（DATE_SUB函数）
-- 用途：作为基准数据计算同比增长率，评估业务增长情况
last_year_data AS (
    SELECT
        bap.营销中心,
        bap.活动编码,
        bap.活动名称,
        bap.产品编码,
        bap.产品名称,
        bap.SKU,
        -- 去年总销量(箱) - 暂时设置为0，需要从销量数据表获取
        0 AS 去年总销量_箱,
        -- 去年总扫码量（根据参与记录ID去重）
        COUNT(DISTINCT caard.participation_record_id) AS 去年总扫码量,
        -- 去年扫码人数（根据用户ID去重）
        COUNT(DISTINCT caard.user_id) AS 去年扫码人数,
        -- 去年扫码金额（红包）- 微信红包类型
        SUM(CASE WHEN caard.send_type = 'WX' THEN caard.winning_amount ELSE 0 END) AS 去年扫码金额_红包,
        -- 去年非红包金额（实物奖等）- 非微信红包类型
        SUM(CASE WHEN caard.send_type != 'WX' THEN caard.winning_amount ELSE 0 END) AS 去年非红包金额_实物奖等,
        -- 去年扫码比 = 扫码量 / 销量（暂时设置为0，需要销量数据）
        0 AS 去年扫码比
    FROM base_activity_product bap
    LEFT JOIN iceberg.crb_edw_dws_sales.dws_sales_consumer_activity_award_record_df caard
        ON caard.activity_id = bap.活动编码
        AND caard.marketorg = bap.marketorg
        -- 去年同期时间段过滤条件（将当前时间段向前推移一年）
        AND caard.distribution_time >= DATE_SUB('${初期开始时间}', INTERVAL 1 YEAR)
        AND caard.distribution_time <= DATE_SUB('${期末结束时间}', INTERVAL 1 YEAR)
        ${if(len(初期开始时间)!=0 && len(期末结束时间)!=0," and caard.distribution_time BETWEEN 
        ' DATE_SUB("+初期开始时间+", INTERVAL 1 YEAR)' and ' DATE_SUB("+期末结束时间+", INTERVAL 1 YEAR)'","")}
    GROUP BY
        bap.营销中心,
        bap.活动编码,
        bap.活动名称,
        bap.产品编码,
        bap.产品名称,
        bap.SKU
)

-- 主查询：整合所有时间段数据并计算同比指标
-- 连接策略：使用FULL OUTER JOIN确保所有时间段数据的完整性
-- 同比计算：采用标准同比公式 (当前值-去年值)/去年值*100%，并处理除零异常
-- 输出格式：同比结果以百分比字符串形式输出，便于报表展示
SELECT
    -- 基础信息字段
    COALESCE(ipd.营销中心, fpd.营销中心, lyd.营销中心) AS 营销中心,
    COALESCE(ipd.活动编码, fpd.活动编码, lyd.活动编码) AS 活动ID,
    COALESCE(ipd.活动名称, fpd.活动名称, lyd.活动名称) AS 活动名称,
    COALESCE(ipd.SKU, fpd.SKU, lyd.SKU) AS SKU,
    COALESCE(ipd.产品名称, fpd.产品名称, lyd.产品名称) AS 产品名称,
    COALESCE(ipd.产品编码, fpd.产品编码, lyd.产品编码) AS 产品编码,
    COALESCE(ipd.上市时间, CAST(NULL AS DATE)) AS 上市时间,

    -- 初期所选时间段指标
    COALESCE(ipd.初期总销量_箱, 0) AS 初期总销量_箱,
    COALESCE(ipd.初期总扫码量, 0) AS 初期总扫码量,
    COALESCE(ipd.初期扫码人数, 0) AS 初期扫码人数_去重,
    COALESCE(ipd.初期扫码金额_红包, 0) AS 初期扫码金额_红包,
    COALESCE(ipd.初期非红包金额_实物奖等, 0) AS 初期非红包金额_实物奖等,
    COALESCE(ipd.初期人均扫码量, 0) AS 初期人均扫码量,
    COALESCE(ipd.初期人均扫码金额, 0) AS 初期人均扫码金额_元,
    COALESCE(ipd.初期次均扫码金额, 0) AS 初期次均扫码金额_元,
    COALESCE(ipd.初期扫码比, 0) AS 初期扫码比,

    -- 期末所选时间段指标
    COALESCE(fpd.期末总销量_箱, 0) AS 期末总销量_箱,
    COALESCE(fpd.期末总扫码量, 0) AS 期末总扫码量,
    COALESCE(fpd.期末扫码人数, 0) AS 期末扫码人数_去重,
    COALESCE(fpd.期末扫码金额_红包, 0) AS 期末扫码金额_红包,
    COALESCE(fpd.期末非红包金额_实物奖等, 0) AS 期末非红包金额_实物奖等,
    COALESCE(fpd.期末扫码比, 0) AS 期末扫码比,

    -- 同比去年指标（计算同比增长率，格式：当前值相对去年值的增长百分比）
    CASE
        WHEN COALESCE(lyd.去年总销量_箱, 0) = 0 THEN
            CASE WHEN COALESCE(ipd.初期总销量_箱, 0) + COALESCE(fpd.期末总销量_箱, 0) > 0 THEN '100%' ELSE '0%' END
        ELSE CONCAT(ROUND(((COALESCE(ipd.初期总销量_箱, 0) + COALESCE(fpd.期末总销量_箱, 0)) - COALESCE(lyd.去年总销量_箱, 0)) * 100.0 / COALESCE(lyd.去年总销量_箱, 1), 2), '%')
    END AS 总销量_箱_同比,

    CASE
        WHEN COALESCE(lyd.去年总扫码量, 0) = 0 THEN
            CASE WHEN COALESCE(ipd.初期总扫码量, 0) + COALESCE(fpd.期末总扫码量, 0) > 0 THEN '100%' ELSE '0%' END
        ELSE CONCAT(ROUND(((COALESCE(ipd.初期总扫码量, 0) + COALESCE(fpd.期末总扫码量, 0)) - COALESCE(lyd.去年总扫码量, 0)) * 100.0 / COALESCE(lyd.去年总扫码量, 1), 2), '%')
    END AS 总扫码量同比,

    CASE
        WHEN COALESCE(lyd.去年扫码人数, 0) = 0 THEN
            CASE WHEN COALESCE(ipd.初期扫码人数, 0) + COALESCE(fpd.期末扫码人数, 0) > 0 THEN '100%' ELSE '0%' END
        ELSE CONCAT(ROUND(((COALESCE(ipd.初期扫码人数, 0) + COALESCE(fpd.期末扫码人数, 0)) - COALESCE(lyd.去年扫码人数, 0)) * 100.0 / COALESCE(lyd.去年扫码人数, 1), 2), '%')
    END AS 扫码人数_去重_同比,

    CASE
        WHEN COALESCE(lyd.去年扫码金额_红包, 0) = 0 THEN
            CASE WHEN COALESCE(ipd.初期扫码金额_红包, 0) + COALESCE(fpd.期末扫码金额_红包, 0) > 0 THEN '100%' ELSE '0%' END
        ELSE CONCAT(ROUND(((COALESCE(ipd.初期扫码金额_红包, 0) + COALESCE(fpd.期末扫码金额_红包, 0)) - COALESCE(lyd.去年扫码金额_红包, 0)) * 100.0 / COALESCE(lyd.去年扫码金额_红包, 1), 2), '%')
    END AS 扫码金额_红包_同比,

    CASE
        WHEN COALESCE(lyd.去年非红包金额_实物奖等, 0) = 0 THEN
            CASE WHEN COALESCE(ipd.初期非红包金额_实物奖等, 0) + COALESCE(fpd.期末非红包金额_实物奖等, 0) > 0 THEN '100%' ELSE '0%' END
        ELSE CONCAT(ROUND(((COALESCE(ipd.初期非红包金额_实物奖等, 0) + COALESCE(fpd.期末非红包金额_实物奖等, 0)) - COALESCE(lyd.去年非红包金额_实物奖等, 0)) * 100.0 / COALESCE(lyd.去年非红包金额_实物奖等, 1), 2), '%')
    END AS 非红包金额_实物奖等_同比,

    CASE
        WHEN COALESCE(lyd.去年扫码比, 0) = 0 THEN
            CASE WHEN COALESCE(ipd.初期扫码比, 0) + COALESCE(fpd.期末扫码比, 0) > 0 THEN '100%' ELSE '0%' END
        ELSE CONCAT(ROUND(((COALESCE(ipd.初期扫码比, 0) + COALESCE(fpd.期末扫码比, 0)) - COALESCE(lyd.去年扫码比, 0)) * 100.0 / COALESCE(lyd.去年扫码比, 1), 2), '%')
    END AS 扫码比同比

-- 表连接：使用FULL OUTER JOIN确保所有时间段的数据都能显示
FROM initial_period_data ipd
FULL OUTER JOIN final_period_data fpd
    ON ipd.营销中心 = fpd.营销中心
    AND ipd.活动编码 = fpd.活动编码
    AND ipd.产品编码 = fpd.产品编码
FULL OUTER JOIN last_year_data lyd
    ON COALESCE(ipd.营销中心, fpd.营销中心) = lyd.营销中心
    AND COALESCE(ipd.活动编码, fpd.活动编码) = lyd.活动编码
    AND COALESCE(ipd.产品编码, fpd.产品编码) = lyd.产品编码

-- 结果排序：按营销中心、活动ID、产品编码排序
ORDER BY
    营销中心,
    活动ID,
    产品编码