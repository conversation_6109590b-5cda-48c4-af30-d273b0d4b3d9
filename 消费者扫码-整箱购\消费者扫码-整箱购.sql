WITH base_code AS(
SELECT
  fbpad.marketorg,
  fbpad.marketorgnm AS 营销中心,
  fbpad.code AS 活动编码,
  fbpad.name AS 活动名称,
  apmd.erp_product_code AS 产品编码,
  apmd.erp_product_name AS 产品名称,
  apmd.product_abbreviation AS SKU
FROM
  iceberg.crb_edw_dim_sales.dim_sales_full_box_purchase_activity_df fbpad
LEFT JOIN iceberg.crb_edw_dim_sales.dim_sales_activity_product_mapping_df apmd 
ON fbpad.marketorg = apmd.marketorg
AND apmd.statistical_dimension_code = fbpad.code
)


SELECT
  bsc.营销中心,
  bsc.活动编码,
  bsc.活动名称,
  bsc.产品编码,
  bsc.产品名称,
  bsc.SKU,
  caard.marketorg AS 营销中心编码,
  COUNT(DISTINCT caard.participation_record_id) AS 总扫码量,
  COUNT(DISTINCT caard.user_id) AS 总扫码人数,
  SUM(caard.winning_amount) AS 总扫码金额,
  SUM(CASE WHEN caard.send_type = 'WX' THEN caard.winning_amount ELSE 0 END) AS 红包总中奖金额,
  SUM(CASE WHEN caard.send_type != 'WX' THEN caard.winning_amount ELSE 0 END) AS 非红包总中奖金额,
  MIN(caard.distribution_time) AS 第一次扫码时间,
  CASE 
    WHEN COUNT(DISTINCT caard.user_id) = 0 THEN 0
    ELSE COUNT(DISTINCT caard.participation_record_id) * 1.0 / COUNT(DISTINCT caard.user_id)
  END AS 人均扫码量,
  CASE 
    WHEN COUNT(DISTINCT caard.user_id) = 0 THEN 0
    ELSE SUM(caard.winning_amount) / COUNT(DISTINCT caard.user_id)
  END AS 人均扫码金额,
  CASE 
    WHEN COUNT(DISTINCT caard.participation_record_id) = 0 THEN 0
    ELSE SUM(caard.winning_amount) / COUNT(DISTINCT caard.participation_record_id)
  END AS 次均扫码金额
FROM base_code bsc
  LEFT JOIN 
  iceberg.crb_edw_dws_sales.dws_sales_consumer_activity_award_record_df caard ON 
  caard.activity_id = bsc.活动编码
  AND caard.marketorg = bsc.marketorg
   WHERE caard.distribution_time >= CURRENT_DATE - INTERVAL '7' DAY
GROUP BY
  bsc.营销中心,
  bsc.活动编码,
  bsc.活动名称,
  bsc.产品编码,
  bsc.产品名称,
  bsc.SKU,
  caard.marketorg

	